import{S as a,_ as e}from"./index-F8MgmgZV.js";import{l as t,E as i,j as s}from"./element-plus-BiAL0NdQ.js";import{u as n}from"./vue-router-D0b9rEnV.js";import{d as l,c as r,a as d,b as o,D as u,K as c,I as p,J as v,o as m,r as b,s as g,C as y,F as _,a1 as h,E as f,q as k}from"./vue-vendor-D6tHD5lA.js";import{u as j,a as w}from"./utils-common-PdkFOSu3.js";import{u as C}from"./app-stores-CLUCXxRF.js";import"./app-assets-DXWh4tep.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./crypto-vendor-CkZKNqwc.js";import"./element-icons-Cfq32zG_.js";import"./vendor-lodash-D0OfQ6x6.js";const I={class:"product-image"},q=["src","alt"],x={class:"product-info"},T={class:"product-name"},N={class:"price-view-container"},P={class:"price-section"},L={class:"current-price"},D={class:"price-integer"},M={class:"price-decimal"},z={class:"price-label"},Q={class:"view-section"},W={class:"view-count"},B={class:"view-label"},S={class:"time-button-container"},A={class:"time-section"},F={class:"time-info"},U={class:"time-label"},E={class:"time-value"},R={class:"schedule-info"},J={class:"schedule-label"},K={class:"schedule-value"},G={class:"button-section"},H=e(l({__name:"BiddingCard",props:{buttonType:{default:"record"},id:{},pmhId:{default:0},bdName:{},startTime:{},endTime:{},bdPic:{},bdQipaijia:{},qpjDanwie:{},bdWeiguan:{},timeLabel:{default:"截止报名"},scheduleLabel:{default:"预计开始"},status:{default:0}},emits:["cardClick","buttonClick"],setup(e,{emit:t}){const i=e,s=t,n=r(()=>1===i.status),l=r(()=>6===i.status),b=r(()=>1===i.status&&"record"===i.buttonType),g=r(()=>{switch(i.status){case 1:return"即将开始";case 5:return"交易完成";case 6:return"核实中";default:return"未知状态"}}),y=r(()=>"cancel"===i.buttonType?"取消收藏":"出价记录"),_=r(()=>Math.floor(Number(i.bdQipaijia))),h=r(()=>(100*(Number(i.bdQipaijia)-Math.floor(Number(i.bdQipaijia)))).toFixed(0).padStart(2,"0")),f=()=>{s("cardClick",{productId:i.id.toString(),pmhId:i.pmhId.toString()})},k=()=>{s("buttonClick",{productId:i.id.toString(),pmhId:i.pmhId.toString(),type:i.buttonType})};return(e,t)=>{const i=a;return m(),d("div",{class:"auction-card",onClick:f},[o("div",{class:u(["status-tag",{"status-active":n.value,"status-verifying":l.value}])},[o("span",null,c(g.value),1)],2),o("div",I,[o("img",{src:e.bdPic,alt:e.bdName},null,8,q)]),o("div",x,[o("span",T,c(e.bdName),1),o("div",N,[o("div",P,[o("div",L,[o("span",D,c(_.value),1),o("span",M,"."+c(h.value),1)]),o("div",z,[p(i,{iconName:"auction-action",class:"price-icon"}),o("span",null,c(e.qpjDanwie),1)])]),t[1]||(t[1]=o("div",{class:"divider"},null,-1)),o("div",Q,[o("div",W,c(e.bdWeiguan),1),o("div",B,[p(i,{iconName:"auction-view",class:"view-icon"}),t[0]||(t[0]=o("span",null,"围观(次)",-1))])])]),o("div",S,[o("div",A,[o("div",F,[o("span",U,c(e.timeLabel),1),o("span",E,c(e.endTime),1)]),o("div",R,[o("span",J,c(e.scheduleLabel),1),o("span",K,c(e.startTime),1)])]),o("div",G,[o("div",{class:u(["action-button",{"button-active":b.value,"button-cancel":"cancel"===e.buttonType}]),onClick:v(k,["stop"])},[o("span",null,c(y.value),1),p(i,{iconName:"auction-arrows-right",class:"button-icon"})],2)])])])])}}}),[["__scopeId","data-v-c77d0db3"]]),O={class:"my-favorites"},V={class:"page-header"},X={key:0,class:"loading-container"},Y={key:1,class:"empty-container"},Z={key:2,class:"auction-grid"},$={key:3,class:"pagination-container"},aa=e(l({__name:"MyFavorites",setup(a){const e=n(),l=C(),r=b(!1),u=b([]),v=b(1),I=b(12),q=b(0),x=async()=>{try{r.value=!0;const a=l.userInfo;if(!(null==a?void 0:a.id))return void i.error("请先登录");const e={member_id:a.id,page:v.value,type:1},t=await j.getMyFavoriteAuctions(e);if(1===t.code){const a=t.data.data.map(async a=>({id:a.id,pmhId:a.pmh_id||0,bdName:a.bd_title,startTime:a.start_time_name.split("年")[1],endTime:a.start_time_name.split("年")[1],bdPic:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/"+a.bd_url,bdQipaijia:a.bd_qipaijia,qpjDanwie:a.qpj_danwie,bdWeiguan:a.bd_weiguan,timeLabel:"截止报名",scheduleLabel:"预计开始",status:a.bd_status})),e=await Promise.all(a);u.value=e,q.value=t.data.total||0}else i.error(t.msg||"获取收藏列表失败")}catch(a){i.error("获取收藏列表失败")}finally{r.value=!1}},T=a=>{v.value=a,x()},N=a=>{e.push({name:"auctionDetail",query:{id:a.productId,pmhId:a.pmhId}})},P=async a=>{var e;try{if("confirm"===await s.confirm("确定要取消收藏这个标的吗？","取消收藏",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})){const t={member_id:(null==(e=l.userInfo)?void 0:e.id)||0,bd_id:Number(a.productId),isquxiao:0},s=await w.favoriteTarget(t);1===s.code?(i.success("取消收藏成功"),x()):i.error(s.msg||"取消收藏失败")}}catch(t){}};return g(()=>{x()}),(a,e)=>{const i=t;return m(),d("div",O,[o("div",V,[e[1]||(e[1]=o("h2",null,"我的收藏",-1)),o("p",null,"共 "+c(q.value)+" 条收藏记录",1)]),r.value?(m(),d("div",X,e[2]||(e[2]=[o("div",{class:"loading-text"},"加载中...",-1)]))):r.value||0!==u.value.length?(m(),d("div",Z,[(m(!0),d(_,null,h(u.value,a=>(m(),f(H,k({key:a.id},{ref_for:!0},a,{"button-type":"cancel",onCardClick:N,onButtonClick:P}),null,16))),128))])):(m(),d("div",Y,e[3]||(e[3]=[o("div",{class:"empty-text"},"暂无收藏记录",-1)]))),q.value>0?(m(),d("div",$,[p(i,{"current-page":v.value,"onUpdate:currentPage":e[0]||(e[0]=a=>v.value=a),"page-size":I.value,total:q.value,layout:"prev, pager, next, jumper",onCurrentChange:T},null,8,["current-page","page-size","total"])])):y("",!0)])}}}),[["__scopeId","data-v-755c9ce1"]]),ea={class:"my-registrations"},ta={class:"page-header"},ia={key:0,class:"loading-container"},sa={key:1,class:"empty-container"},na={key:2,class:"auction-grid"},la={key:3,class:"pagination-container"},ra=e(l({__name:"MyRegistrations",setup(a){const e=n(),s=C(),l=b(!1),r=b([]),u=b(1),v=b(12),w=b(0),I=async()=>{try{l.value=!0;const a=s.userInfo;if(!(null==a?void 0:a.id))return void i.error("请先登录");const e={member_id:a.id,page:u.value,type:1},t=await j.getMyRegisteredAuctions(e);if(1===t.code){const a=t.data.data.map(async a=>({id:a.id,pmhId:a.pmh_id||0,bdName:a.bd_title,startTime:a.start_time_name,endTime:a.end_time_name,bdPic:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/"+a.bd_url,bdQipaijia:a.bd_qipaijia,qpjDanwie:a.qpj_danwie,bdWeiguan:a.bd_weiguan,timeLabel:"截止报名",scheduleLabel:"预计开始",status:a.bd_status})),e=await Promise.all(a);r.value=e,w.value=t.data.total||0}else i.error(t.msg||"获取报名列表失败")}catch(a){i.error("获取报名列表失败")}finally{l.value=!1}},q=a=>{u.value=a,I()},x=a=>{e.push({name:"auctionDetail",query:{id:a.productId}})},T=a=>{i.info("查看出价记录功能待实现")};return g(()=>{I()}),(a,e)=>{const i=t;return m(),d("div",ea,[o("div",ta,[e[1]||(e[1]=o("h2",null,"我的报名",-1)),o("p",null,"共 "+c(w.value)+" 条报名记录",1)]),l.value?(m(),d("div",ia,e[2]||(e[2]=[o("div",{class:"loading-text"},"加载中...",-1)]))):l.value||0!==r.value.length?(m(),d("div",na,[(m(!0),d(_,null,h(r.value,a=>(m(),f(H,k({key:a.id},{ref_for:!0},a,{"button-type":"record",onCardClick:x,onButtonClick:T}),null,16))),128))])):(m(),d("div",sa,e[3]||(e[3]=[o("div",{class:"empty-text"},"暂无报名记录",-1)]))),w.value>0?(m(),d("div",la,[p(i,{"current-page":u.value,"onUpdate:currentPage":e[0]||(e[0]=a=>u.value=a),"page-size":v.value,total:w.value,layout:"prev, pager, next, jumper",onCurrentChange:q},null,8,["current-page","page-size","total"])])):y("",!0)])}}}),[["__scopeId","data-v-c3a23dd9"]]),da={class:"my-participations"},oa={class:"page-header"},ua={key:0,class:"loading-container"},ca={key:1,class:"empty-container"},pa={key:2,class:"auction-grid"},va={key:3,class:"pagination-container"},ma=e(l({__name:"MyParticipations",setup(a){const e=n(),s=C(),l=b(!1),r=b([]),u=b(1),v=b(12),w=b(0),I=async()=>{try{l.value=!0;const a=s.userInfo;if(!(null==a?void 0:a.id))return void i.error("请先登录");const e={member_id:a.id,page:u.value,type:1},t=await j.getMyParticipatedAuctions(e);if(1===t.code){const a=t.data.data.map(async a=>({id:a.id,pmhId:a.pmh_id||0,bdName:a.bd_title,startTime:a.start_time_name,endTime:a.end_time_name,bdPic:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/"+a.bd_url,bdQipaijia:a.bd_qipaijia,qpjDanwie:a.qpj_danwie,bdWeiguan:a.bd_weiguan,timeLabel:"截止报名",scheduleLabel:"预计开始",status:a.bd_status})),e=await Promise.all(a);r.value=e,w.value=t.data.total||0}else i.error(t.msg||"获取参与列表失败")}catch(a){i.error("获取参与列表失败")}finally{l.value=!1}},q=a=>{u.value=a,I()},x=a=>{e.push({name:"auctionDetail",query:{id:a.productId}})},T=a=>{i.info("查看出价记录功能待实现")};return g(()=>{I()}),(a,e)=>{const i=t;return m(),d("div",da,[o("div",oa,[e[1]||(e[1]=o("h2",null,"参与标的",-1)),o("p",null,"共 "+c(w.value)+" 条参与记录",1)]),l.value?(m(),d("div",ua,e[2]||(e[2]=[o("div",{class:"loading-text"},"加载中...",-1)]))):l.value||0!==r.value.length?(m(),d("div",pa,[(m(!0),d(_,null,h(r.value,a=>(m(),f(H,k({key:a.id},{ref_for:!0},a,{"button-type":"record",onCardClick:x,onButtonClick:T}),null,16))),128))])):(m(),d("div",ca,e[3]||(e[3]=[o("div",{class:"empty-text"},"暂无参与记录",-1)]))),w.value>0?(m(),d("div",va,[p(i,{"current-page":u.value,"onUpdate:currentPage":e[0]||(e[0]=a=>u.value=a),"page-size":v.value,total:w.value,layout:"prev, pager, next, jumper",onCurrentChange:q},null,8,["current-page","page-size","total"])])):y("",!0)])}}}),[["__scopeId","data-v-2c7d30c8"]]),ba={class:"my-won-auctions"},ga={class:"page-header"},ya={key:0,class:"loading-container"},_a={key:1,class:"empty-container"},ha={key:2,class:"auction-grid"},fa={key:3,class:"pagination-container"},ka=e(l({__name:"MyWonAuctions",setup(a){const e=n(),s=C(),l=b(!1),r=b([]),u=b(1),v=b(12),w=b(0),I=async()=>{try{l.value=!0;const a=s.userInfo;if(!(null==a?void 0:a.id))return void i.error("请先登录");const e={member_id:a.id,page:u.value,type:1},t=await j.getMyWonAuctions(e);if(1===t.code){const a=t.data.data.map(async a=>({id:a.id,pmhId:a.pmh_id||0,bdName:a.bd_title,startTime:a.start_time_name,endTime:a.end_time_name,bdPic:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/"+a.bd_url,bdQipaijia:a.bd_qipaijia,qpjDanwie:a.qpj_danwie,bdWeiguan:a.bd_weiguan,timeLabel:"截止报名",scheduleLabel:"预计开始",status:a.bd_status})),e=await Promise.all(a);r.value=e,w.value=t.data.total||0}else i.error(t.msg||"获取竞得列表失败")}catch(a){i.error("获取竞得列表失败")}finally{l.value=!1}},q=a=>{u.value=a,I()},x=a=>{e.push({name:"auctionDetail",query:{id:a.productId}})},T=a=>{i.info("查看出价记录功能待实现")};return g(()=>{I()}),(a,e)=>{const i=t;return m(),d("div",ba,[o("div",ga,[e[1]||(e[1]=o("h2",null,"竞得标的",-1)),o("p",null,"共 "+c(w.value)+" 条竞得记录",1)]),l.value?(m(),d("div",ya,e[2]||(e[2]=[o("div",{class:"loading-text"},"加载中...",-1)]))):l.value||0!==r.value.length?(m(),d("div",ha,[(m(!0),d(_,null,h(r.value,a=>(m(),f(H,k({key:a.id},{ref_for:!0},a,{"button-type":"record",onCardClick:x,onButtonClick:T}),null,16))),128))])):(m(),d("div",_a,e[3]||(e[3]=[o("div",{class:"empty-text"},"暂无竞得记录",-1)]))),w.value>0?(m(),d("div",fa,[p(i,{"current-page":u.value,"onUpdate:currentPage":e[0]||(e[0]=a=>u.value=a),"page-size":v.value,total:w.value,layout:"prev, pager, next, jumper",onCurrentChange:q},null,8,["current-page","page-size","total"])])):y("",!0)])}}}),[["__scopeId","data-v-2613a076"]]),ja={class:"bidding-manage-container"},wa={class:"bidding-sidebar"},Ca={class:"sidebar-nav"},Ia=["onClick"],qa={class:"nav-text"},xa={class:"bidding-content"},Ta=e(l({__name:"index",setup(e){const t=b("favorites"),i=[{key:"favorites",label:"我的收藏",icon:"login-data-attention"},{key:"registrations",label:"我的报名",icon:"login-data-real-name"},{key:"participations",label:"参与标的",icon:"login-data-security"},{key:"won",label:"竞得标的",icon:"login-data-personal"}];return(e,s)=>(m(),d("div",ja,[o("div",wa,[s[0]||(s[0]=o("div",{class:"sidebar-title"},"竞拍管理",-1)),s[1]||(s[1]=o("div",{class:"sidebar-divider"},null,-1)),o("div",Ca,[(m(),d(_,null,h(i,e=>o("div",{key:e.key,class:u(["nav-item",{active:t.value===e.key}]),onClick:a=>{return i=e.key,void(t.value=i);var i}},[p(a,{iconName:e.icon,class:"nav-icon"},null,8,["iconName"]),o("span",qa,c(e.label),1)],10,Ia)),64))])]),o("div",xa,["favorites"===t.value?(m(),f(aa,{key:0})):y("",!0),"registrations"===t.value?(m(),f(ra,{key:1})):y("",!0),"participations"===t.value?(m(),f(ma,{key:2})):y("",!0),"won"===t.value?(m(),f(ka,{key:3})):y("",!0)])]))}}),[["__scopeId","data-v-4f730a4d"]]);export{Ta as default};
