import{p as e,l as a,E as t,j as o}from"./element-plus-BiAL0NdQ.js";import{d as s,r,t as n,s as i,x as l,a as u,b as c,C as d,I as m,F as p,a1 as v,u as f,E as g,O as y,P as w,K as h,J as j,X as I,q as k,o as N}from"./vue-vendor-D6tHD5lA.js";import{a as U,u as C}from"./vue-router-D0b9rEnV.js";import{P as b}from"./PropertyCard-DPee-D34.js";import{C as T}from"./Carousel-BFQyt06G.js";import{S as x,_ as P}from"./index-F8MgmgZV.js";import{n as q}from"./utils-common-PdkFOSu3.js";import{a as L}from"./app-stores-CLUCXxRF.js";import"./element-icons-Cfq32zG_.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./vendor-lodash-D0OfQ6x6.js";import"./app-assets-DXWh4tep.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./crypto-vendor-CkZKNqwc.js";const _={class:"bidding"},F={class:"carousel-container"},S={class:"section"},B={class:"section-title"},D={class:"title-container"},E={class:"content-container"},W={key:0,class:"loading-container"},z={key:1,class:"auction-cards"},H={key:2,class:"pagination-container"},$={key:3,class:"empty-state"},G={key:0,class:"freedom-login-float"},J=["src"],K={class:"float-user-info-section"},O={class:"float-user-info-avatar"},R=["src"],X={class:"float-user-info-content"},Z={class:"float-user-info-name"},A={class:"float-user-info-phone"},M={class:"float-menu-list-section"},Q=P(s({__name:"FreedomHome",setup(s){const P=C(),Q=U(),V=L(),Y=r(""),ee=r(1),ae=r(8),te=r(0),oe=r(!1),se=r([{image:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/free-banner_1754963528553.jpg",title:"",price:"",description:""},{image:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250603/17489456557790.png",title:"推广banner图展示位"},{image:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png",title:"推广banner图展示位"}]),re=r([]),ne=r(!1),ie=async(e=ee.value)=>{try{oe.value=!0;const a=await q.getTradeZonePageList({pageNo:e,pageSize:ae.value,queryHgySupplyDemandDto:{infoTitle:Y.value}});if(a.success||200===a.code){const t=a.result;t&&t.records?(re.value=t.records.map(e=>({productId:e.id,productName:e.infoTitle,productImage:e.attachment,currentPrice:e.price,priceUnit:e.priceUnit||"元",viewCount:e.viewNum||0,enterpriseLogo:e.enterpriseLogo||"",enterpriseName:e.enterpriseName||e.company,enterpriseType:e.enterpriseType||"企业",status:e.type,statusName:e.type_dictText,productCount:e.quantity||1,productCountUnit:e.unit||"批",productWeight:e.productWeight||0,productWeightUnit:e.productWeightUnit||"吨",productPower:e.viewNum,productPowerUnit:e.productPowerUnit})),te.value=t.total||0,ee.value=e):(re.value=[],te.value=0)}else t.error(a.message||a.msg||"获取数据失败")}catch(a){t.error("获取数据失败，请稍后重试")}finally{oe.value=!1}},le=async e=>{if(!V.checkFreedomLogin())try{await o.confirm("访问资产详情需要登录自由交易账号，是否前往登录？","需要登录",{confirmButtonText:"去登录",cancelButtonText:"取消",type:"warning"});P.currentRoute.value.fullPath;return void P.push({name:"freedomLogin",query:{redirect:`/propertyDetail?id=${e.productId}&crumbsTitle=自由交易&type=4`}})}catch{return}const a=re.value.find(a=>a.productId===e.productId),t=(null==a?void 0:a.status)||"4";P.push({name:"propertyDetail",query:{id:e.productId,crumbsTitle:"自由交易",type:t}})},ue=e=>{ee.value=e,ie(e)},ce=e=>{ee.value=1,ie(1),t.success(`正在搜索: ${e}`)},de=()=>{ne.value=!ne.value},me=()=>{ne.value=!1},pe=e=>{const a=e.target,t=document.querySelector(".freedom-user-dropdown"),o=document.querySelector(".float-avatar");t&&o&&!t.contains(a)&&!o.contains(a)&&me()},ve=async()=>{try{await o.confirm("确定要退出自由交易登录吗？","退出登录",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await V.freedomLogout(),me(),t.success("已退出自由交易登录")}catch{}};return n(()=>Q.query.keyword,e=>{e&&"string"==typeof e&&(Y.value=e,ce(e))},{immediate:!0}),i(()=>{V.initFreedomUserState(),ie(1);const e=Q.query.keyword;e&&"string"==typeof e&&(Y.value=e,ce(e)),document.addEventListener("click",pe)}),l(()=>{document.removeEventListener("click",pe)}),(t,o)=>{var s,r,n,i;const l=e,U=a;return N(),u(p,null,[c("div",_,[c("div",F,[m(T,{items:se.value,autoplay:!0,interval:5e3},null,8,["items"])]),c("div",S,[c("div",B,[c("div",D,[m(x,{iconName:"freedom-assets-disposal",className:"title-icon"}),o[2]||(o[2]=c("span",null,"资产处置",-1))])]),c("div",E,[oe.value?(N(),u("div",W,[m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""}),m(l,{rows:2,animated:""})])):(N(),u("div",z,[(N(!0),u(p,null,v(re.value,e=>(N(),g(b,k({key:e.productId},{ref_for:!0},e,{onClick:le}),null,16))),128))])),!oe.value&&te.value>0?(N(),u("div",H,[m(U,{"current-page":ee.value,"onUpdate:currentPage":o[0]||(o[0]=e=>ee.value=e),"page-size":ae.value,total:te.value,layout:"total, prev, pager, next, jumper",onCurrentChange:ue},null,8,["current-page","page-size","total"])])):d("",!0),oe.value||0!==re.value.length?d("",!0):(N(),u("div",$,o[3]||(o[3]=[c("div",{class:"empty-text"},"暂无数据",-1)])))])])]),f(V).isFreedomLoggedIn?(N(),u("div",G,[c("div",{class:"float-avatar",onClick:de},[(null==(s=f(V).freedomUserInfo)?void 0:s.userInfo.avatar)?(N(),u("img",{key:0,src:f(V).freedomUserInfo.userInfo.avatar,alt:"用户头像",class:"float-user-avatar-img"},null,8,J)):(N(),g(x,{key:1,iconName:"user",className:"float-avatar-icon"}))]),(N(),g(I,{to:"body"},[y(c("div",{class:"freedom-user-dropdown",onClick:o[1]||(o[1]=j(()=>{},["stop"]))},[c("div",K,[c("div",O,[(null==(r=f(V).freedomUserInfo)?void 0:r.userInfo.avatar)?(N(),u("img",{key:0,src:f(V).freedomUserInfo.userInfo.avatar,alt:"用户头像",class:"float-dropdown-avatar-img"},null,8,R)):(N(),g(x,{key:1,iconName:"user",className:"float-info-avatar-icon"}))]),c("div",X,[c("span",Z,h((null==(n=f(V).freedomUserInfo)?void 0:n.userInfo.username)||"自由交易用户"),1),c("span",A,h((null==(i=f(V).freedomUserInfo)?void 0:i.userInfo.phone)||""),1)])]),c("div",M,[c("div",{class:"float-menu-item float-logout-item",onClick:ve},[m(x,{iconName:"login-menu-logout",className:"float-menu-icon float-logout-icon"}),o[4]||(o[4]=c("span",null,"退出登录",-1))])])],512),[[w,ne.value]])]))])):d("",!0)],64)}}}),[["__scopeId","data-v-76dfcac0"]]);export{Q as default};
