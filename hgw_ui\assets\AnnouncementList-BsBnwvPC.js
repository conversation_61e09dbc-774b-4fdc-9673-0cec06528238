import{l as a}from"./element-plus-BiAL0NdQ.js";import{u as e}from"./vue-router-D0b9rEnV.js";import{a as t,_ as i}from"./index-F8MgmgZV.js";import{o as n,b as o}from"./utils-common-PdkFOSu3.js";import{d as s,r as u,c as l,s as c,a as m,b as r,F as p,a1 as d,E as v,C as g,D as y,K as h,I as C,o as j}from"./vue-vendor-D6tHD5lA.js";import"./element-icons-Cfq32zG_.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./vendor-lodash-D0OfQ6x6.js";import"./app-assets-DXWh4tep.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./app-stores-CLUCXxRF.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./crypto-vendor-CkZKNqwc.js";const b={class:"announcement-list-page"},k={class:"tab-nav"},_=["onClick"],f={class:"list-container"},w={class:"auction-list"},N={key:0,class:"item-divider"},I=i(s({__name:"AnnouncementList",setup(i){const s=e(),I=[{label:"采购公告",value:"purchase"},{label:"销售公告",value:"sale"},{label:"拍卖会公告",value:"auction"}],V=u("purchase"),L=u([]),S=u(!1),x=u(1),A=u(0),T="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/purchase_1754962265712.png",q="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/sale_1754962281689.png",z=async(a=1)=>{S.value=!0;try{let e=[];switch(V.value){case"purchase":e=await(async(a=1)=>{try{const e=await n.getTenderList({cateid:"29",limit:10,page:a});return 1===e.code&&e.data?(A.value=e.data.total,e.data.data.map(a=>({id:a.id.toString(),type:"purchase",title:a.title,image:T,description:a.title,companyName:a.cate_name||"采购公告",timeValue:a.addtime,likeCount:0,commentCount:0,viewCount:0}))):[]}catch(e){return[]}})(a);break;case"sale":e=await(async(a=1)=>{try{const e=await n.getTenderList({cateid:"30",limit:10,page:a});return 1===e.code&&e.data?(A.value=e.data.total,e.data.data.map(a=>({id:a.id.toString(),type:"sale",title:a.title,image:q,description:a.title,companyName:a.cate_name||"销售公告",timeValue:a.addtime,likeCount:0,commentCount:0,viewCount:0}))):[]}catch(e){return[]}})(a);break;case"auction":e=await(async(a=1)=>{try{const e=await o.getAuctionSessionAnnouncementList({page:a});return 1===e.code&&e.data?(A.value=e.data.total,e.data.data.map(a=>({id:a.id.toString(),type:"auction",title:a.pmh_name,image:a.pmh_pic?`https://huigupaimai.oss-cn-beijing.aliyuncs.com/${a.pmh_pic}`:"",description:a.pmh_gonggao||a.pmh_name,companyName:a.parentname||"拍卖会公告",timeValue:a.start_time_name||a.addtime,likeCount:0,commentCount:0,viewCount:0}))):[]}catch(e){return[]}})(a)}L.value=e}catch(e){L.value=[],A.value=0}finally{S.value=!1}},D=l(()=>L.value.filter(a=>a.type===V.value)),E=l(()=>D.value);function F(a){x.value=a,z(a)}return c(()=>{z(1)}),(e,i)=>{const n=a;return j(),m("div",b,[r("div",k,[(j(),m(p,null,d(I,a=>r("div",{key:a.value,class:y(["tab-item",{active:V.value===a.value}]),onClick:e=>function(a){V.value=a,x.value=1,z(1)}(a.value)},h(a.label),11,_)),64))]),r("div",f,[r("div",w,[(j(!0),m(p,null,d(E.value,(a,e)=>(j(),m(p,{key:a.id},[C(t,{productId:a.id,productName:a.title,productImage:a.image||"",description:a.description,companyName:a.companyName,timeValue:a.timeValue,likeCount:a.likeCount,commentCount:a.commentCount,viewCount:a.viewCount,onClick:()=>{return e=a.id,void s.push({name:"announcementInfo-detail",query:{id:e,type:V.value,crumbsTitle:"公告信息"}});var e}},null,8,["productId","productName","productImage","description","companyName","timeValue","likeCount","commentCount","viewCount","onClick"]),e<E.value.length-1?(j(),m("div",N)):g("",!0)],64))),128))]),A.value>10?(j(),v(n,{key:0,class:"pagination",background:"",layout:"prev, pager, next",total:A.value,"page-size":10,"current-page":x.value,onCurrentChange:F},null,8,["total","current-page"])):g("",!0)])])}}}),[["__scopeId","data-v-f4b60768"]]);export{I as default};
