# 服务器配置示例

## 问题说明
当使用Vue Router的History模式时，直接访问或刷新非首页路由会导致404错误。这是因为服务器需要配置回退规则，将所有路由请求都指向index.html。

## 解决方案

### 1. Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/your/hgw_ui;
    index index.html;

    # 处理静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # 处理API请求（如果有）
    location /api/ {
        proxy_pass http://your-api-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # SPA路由回退规则 - 关键配置
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 2. Apache配置
在hgw_ui目录下创建.htaccess文件：
```apache
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /
  RewriteRule ^index\.html$ - [L]
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule . /index.html [L]
</IfModule>
```

### 3. Express.js服务器
```javascript
const express = require('express');
const path = require('path');
const app = express();

// 静态文件服务
app.use(express.static(path.join(__dirname, 'hgw_ui')));

// API路由（如果有）
app.use('/api', require('./api-routes'));

// SPA回退规则
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'hgw_ui/index.html'));
});

app.listen(3000);
```

### 4. IIS配置
在hgw_ui目录下创建web.config文件：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="Handle History Mode and hash fallback" stopProcessing="true">
          <match url="(.*)" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
```

### 5. 简单HTTP服务器（开发测试用）
```bash
# 使用serve包
npm install -g serve
serve -s hgw_ui

# 使用http-server包
npm install -g http-server
http-server hgw_ui -o --spa
```

## 推荐方案

### 开发环境
- 使用Hash模式（已修改）或确保开发服务器支持History模式

### 生产环境
1. **如果可以配置服务器**：使用History模式 + 服务器配置
2. **如果无法配置服务器**：使用Hash模式

## 当前修改
已将路由模式修改为Hash模式，这样可以避免服务器配置问题。URL会变成：
- 首页：`http://your-domain.com/#/`
- 关于页：`http://your-domain.com/#/about`
- 登录页：`http://your-domain.com/#/login`

如果你希望使用更美观的URL（不带#），请按照上述服务器配置示例进行配置。
